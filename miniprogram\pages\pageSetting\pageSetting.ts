import { IAppOption } from "../../../typings/index"
import { BleDataCallback, BleDataHandler, BleSendDataHandler } from "../../lib/ble-data-handler"
import { BluetoothManager } from "../../lib/bluetoothManager"
import toast from "../../settingPageComp/toast"
import { initBLTChange, sendAfter, sendBefore,sendData,
   string2buffer,disConnectSoBack,overSendCount, hexToDec,decimalToHexadecimal, hexToString, reConnection, ab2hex, newVersionSendBefore, newVersionSendSave, newVersionSendAfter } from "../../settingPageComp/index"
import AnalyzeData from "../../settingPageComp/AnalyzeData"

const app = getApp<IAppOption>()
var sBluetoothManager: BluetoothManager

Page({
  data: {
    lmdValue: 2, //灵敏度的切换value值，1选择了低，2选择了中，3高
    silderValue: 1, //滑块的值
    distanceValue: 7, //新增的距离滑块值，单位米，默认为0.7米
    isGetPersonSetting: 0, //true就是选择了有人设置，默认为true
    statusText1: '无人',
    statusText2: 0, //有人-距离值（最接近）
    keepTime: 1, //无人持续时间
    version: '1.0',//查询固件版本
    // timer:0,//倒计时器，当检测无人以后用来延迟变有人
    sendCount: 0,//重试指令次数
    maxSendCount: 2,//最大重试次数
    low_lmdList: [70, 0, 70, 0, 50, 60, 50, 60, 25, 60, 25, 40, 18, 40, 18, 25, 18, 25],
    middle_lmdList: [60, 0, 60, 0, 30, 50, 30, 50, 20, 50, 20, 30, 15, 30, 15, 20, 15, 20],
    height_lmdList: [40, 0, 40, 0, 20, 40, 20, 40, 15, 40, 15, 25, 12, 25, 12, 15, 12, 15],
    isShowToast: false, //是否显示弹窗
    toastSuccess: true, //弹窗的类型 //1为蓝，0为红
    toastText: '操作成功', //弹窗文字
    strConfig: '',//页面的雷达档位对应的灵敏度的16进制数据（需要自己切换一次雷达档为再次做翻译）
    //1.页面初始化的时候需要拿到这个16进制值原始值
    //2.每滑动一次档位，就要更新一次灵敏度和原始值
    //3.每设置一次灵敏度就要重新获取一次原始值
    sendCount_lmd: 0, //发送灵敏度顺序 0 - 8 共9次，发完或者报错归0
    isStudy:true,//是否在学习模式，如果不在，设备也不回复则判定为掉线
    distanceValues: [], // 存储距离值的数组，用于计算平均值
    distanceCalcTimer: 0, // 计算距离平均值的定时器
    sensitivityList: [{
      selectIcon: '/images/SR20-wu-icon/sensitivity-icon/guogao_gaoliang-1.png',
      icon: '/images/SR20-wu-icon/sensitivity-icon/guodi_moren.png',
      name: '过低',
      value: '93660400',
      instructionList: [
        'FD FC FB FA 14 00 07 00 10 00 09 06 03 00 11 00 B6 FA 01 00 12 00 AB 44 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 13 00 76 38 00 00 14 00 69 49 00 00 15 00 D1 37 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 16 00 10 31 00 00 17 00 27 27 00 00 18 00 0A 24 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 19 00 61 23 00 00 1A 00 4B 1D 00 00 1B 00 97 18 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 1C 00 44 13 00 00 1D 00 88 13 00 00 1E 00 96 10 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 1F 00 F9 10 00 00 30 00 93 66 04 00 31 00 9C E6 02 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 32 00 FE 5B 00 00 33 00 E1 27 00 00 34 00 59 26 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 35 00 06 22 00 00 36 00 A6 25 00 00 37 00 B7 20 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 38 00 8A 17 00 00 39 00 95 16 00 00 3A 00 38 13 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 3B 00 36 11 00 00 3C 00 D0 11 00 00 3D 00 87 1B 00 00 04 03 02 01',
        'FD FC FB FA 0E 00 07 00 3E 00 45 12 00 00 3F 00 3F 10 00 00 04 03 02 01'
      ],
      index: 1
    }, {
      selectIcon: '/images/SR20-wu-icon/sensitivity-icon/di_gaoliang.png',
      icon: '/images/SR20-wu-icon/sensitivity-icon/di_moren.png',
      name: '低',
      value: 'DF7E0300',
      instructionList: [
        'FD FC FB FA 14 00 07 00 10 00 D6 66 02 00 11 00 7F 92 01 00 12 00 8C 36 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 13 00 DA 2C 00 00 14 00 52 2E 00 00 15 00 56 2C 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 16 00 F9 26 00 00 17 00 1A 1F 00 00 18 00 A0 1C 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 19 00 1A 1C 00 00 1A 00 45 17 00 00 1B 00 88 13 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 1C 00 4D 0F 00 00 1D 00 84 0F 00 00 1E 00 2D 0D 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 1F 00 7B 0D 00 00 30 00 DF 7E 03 00 31 00 E0 4D 02 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 32 00 13 49 00 00 33 00 AE 1F 00 00 34 00 76 1E 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 35 00 06 1B 00 00 36 00 E8 1D 00 00 37 00 FD 19 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 38 00 B2 12 00 00 39 00 F0 11 00 00 3A 00 44 0F 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 3B 00 AB 0D 00 00 3C 00 26 0E 00 00 3D 00 DE 15 00 00 04 03 02 01',
        'FD FC FB FA 0E 00 07 00 3E 00 83 0E 00 00 3F 00 E8 0C 00 00 04 03 02 01'
      ],
      index: 2
    }, {
      selectIcon: '/images/SR20-wu-icon/sensitivity-icon/zhong_gaoliang.png',
      icon: '/images/SR20-wu-icon/sensitivity-icon/zhong_moren.png',
      name: '中',
      value: 'D2C60200',
      instructionList: [
        'FD FC FB FA 14 00 07 00 10 00 62 E8 01 00 11 00 B6 3F 01 00 12 00 54 2B 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 13 00 A0 23 00 00 14 00 CB 24 00 00 15 00 38 23 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 16 00 F5 1E 00 00 17 00 B4 18 00 00 18 00 BD 16 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 19 00 53 16 00 00 1A 00 7C 12 00 00 1B 00 84 0F 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 1C 00 28 0C 00 00 1D 00 53 0C 00 00 1E 00 77 0A 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 1F 00 B6 0A 00 00 30 00 D2 C6 02 00 31 00 8E D4 01 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 32 00 0B 3A 00 00 33 00 2A 19 00 00 34 00 32 18 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 35 00 77 15 00 00 36 00 C1 17 00 00 37 00 A4 14 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 38 00 DA 0E 00 00 39 00 40 0E 00 00 3A 00 21 0C 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 3B 00 DC 0A 00 00 3C 00 3D 0B 00 00 3D 00 5E 11 00 00 04 03 02 01',
        'FD FC FB FA 0E 00 07 00 3E 00 87 0B 00 00 3F 00 40 0A 00 00 04 03 02 01'
      ],
      index: 3
    }, {
      selectIcon: '/images/SR20-wu-icon/sensitivity-icon/gao_gaoliang.png',
      icon: '/images/SR20-wu-icon/sensitivity-icon/gao_moren.png',
      name: '高',
      value: 'A0340200',
      instructionList: [
        'FD FC FB FA 14 00 07 00 10 00 F0 83 01 00 11 00 F5 FD 00 00 12 00 6A 22 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 13 00 4C 1C 00 00 14 00 3A 1D 00 00 15 00 F9 1B 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 16 00 97 18 00 00 17 00 9F 13 00 00 18 00 10 12 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 19 00 BB 11 00 00 1A 00 AE 0E 00 00 1B 00 53 0C 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 1C 00 A8 09 00 00 1D 00 CA 09 00 00 1E 00 50 08 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 1F 00 82 08 00 00 30 00 A0 34 02 00 31 00 30 74 01 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 32 00 1B 2E 00 00 33 00 FD 13 00 00 34 00 38 13 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 35 00 0D 11 00 00 36 00 DF 12 00 00 37 00 66 10 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 38 00 CC 0B 00 00 39 00 51 0B 00 00 3A 00 A2 09 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 3B 00 A0 08 00 00 3C 00 EE 08 00 00 3D 00 CC 0D 00 00 04 03 02 01',
        'FD FC FB FA 0E 00 07 00 3E 00 28 09 00 00 3F 00 24 08 00 00 04 03 02 01',
      ],
      index: 4
    }, {
      selectIcon: '/images/SR20-wu-icon/sensitivity-icon/guogao_gaoliang.png',
      icon: '/images/SR20-wu-icon/sensitivity-icon/guogao_moren.png',
      name: '过高',
      value: '83060200',
      instructionList: [
        'FD FC FB FA 14 00 07 00 10 00 01 55 01 00 11 00 C1 83 00 00 12 00 88 05 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 13 00 CE 05 00 00 14 00 08 08 00 00 15 00 4E 06 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 16 00 78 05 00 00 17 00 6B 0A 00 00 18 00 69 07 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 19 00 39 04 00 00 1A 00 9C 04 00 00 1B 00 D1 04 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 1C 00 29 05 00 00 1D 00 36 05 00 00 1E 00 3F 05 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 1F 00 78 05 00 00 30 00 83 06 02 00 31 00 80 C6 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 32 00 CE 04 00 00 33 00 F5 07 00 00 34 00 95 0B 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 35 00 38 08 00 00 36 00 9B 08 00 00 37 00 95 0E 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 38 00 97 09 00 00 39 00 25 03 00 00 3A 00 FE 02 00 00 04 03 02 01',
        'FD FC FB FA 14 00 07 00 3B 00 36 03 00 00 3C 00 A5 03 00 00 3D 00 65 03 00 00 04 03 02 01',
        'FD FC FB FA 0E 00 07 00 3E 00 8E 03 00 00 3F 00 8E 03 00 00 04 03 02 01',
      ],
      index: 5
    }],
    sensitivityIndex: -1
  },
  /**打开提示窗 */
  openToast(type = 1,text = '操作成功'){
    if (type ==1 ) {
      this.setData({
        toastText:text,
        toastSuccess:true,
        isShowToast:true})
    }else{
      this.setData({
        toastText:text,
        toastSuccess:false,
        isShowToast:true})
    }
  },
  /** 进入固件版本页面 */
  toVersionPage() {
    sendBefore()
    wx.navigateTo({ url: '../pageFirmware/pageFirmware' })
  },
    /**关闭提示窗 */
  async closeToast(){  
    this.setData({isShowToast:false})
    return true
  },

  /** 设置无人持续时间 */
  setNobodyKeepTimer:function(){
    let that = this
      wx.showModal({
        title: '无人持续时间',
        editable:true,
        placeholderText:'请输入取值范围1-65535',
        success: function (res) {
          if (res.confirm) {//这里是点击了确定以后
            let data = Number(res.content)
            // 拦截非法
            if (Object.is(data, NaN)){
              toast.info('输入值只能为数字！')
              return;
            }
            if (data > 65535){
              toast.info('输入值过大!')
              return;
            }
            if (data < 1) {
              toast.info('输入值过小!')
              return;
            }
            // 执行逻辑
            that.setData({
              keepTime:data
            })
            that.setNobodyKeeptime_radarDistance_Command(data,that.data.silderValue)
          } else {//这里是点击了取消以后
            toast.info('取消操作')
          }
        }
      })
  },
  /**设置-无人持续时间 -和- 雷达距离门 */
  async setNobodyKeeptime_radarDistance_Command(_time=0,_shift=1) {
    const { version } = this.data
    if (version === '2.0') {
      // 版本为2.0设置无人持续时间
      let resultList = []
      let time = decimalToHexadecimal(_time)
      // let shift = decimalToHexadecimal(_shift)
      let data = "FDFCFBFA080007000400" + time + "04030201".replace(/\s/g, '');
      console.log('data => ', data)
      // console.log('这里',data);
      resultList[0] = await newVersionSendBefore()
      resultList[1] = await sendData(data)
      resultList[2] = await newVersionSendSave()
      resultList[3] = await newVersionSendAfter()
      console.log('打印设置距离们执行结果：',resultList);
      for (let i = 0; i < resultList.length; i++) {
        if (resultList[i] === false) { //第二段是为了防止死循环
          return toast.info("请稍后再试")
        }
      }
      this.openToast(1,'设置成功')
      wx.hideLoading()
      return
    }
    let  resultList = []
    let time = decimalToHexadecimal(_time)
    let shift = decimalToHexadecimal(_shift)
    let data = "FD FC FB FA 14 00 60 00 0000 " + shift +"01 00 " + shift + "02 00 " + time + "04 03 02 01".replace(/\s/g, '');
    // console.log('这里',data);
    resultList[0] =  await sendBefore()
    resultList[1] =  await sendData(data)
    resultList[2] =  await sendAfter()
    console.log('打印设置距离们执行结果：',resultList);
    for (let i = 0; i < resultList.length; i++) {
      if (resultList[i] === false) { //第二段是为了防止死循环
        return toast.info("请稍后再试")
      }
    }
    this.openToast(1,'设置成功')
    wx.hideLoading()
  },
  /** 滑动-雷达距离门档位 */
  sliderchanging: function(e:any){ //滑块的取值
    let value:number = e.detail.value
    if (value === 0) {
      this.setData({
        silderValue:1
      })
    }else{
      this.setData({
        silderValue:value
      })
    }
  },
  /** 设置-雷达距离门档位 */
  bindchange:function (e:any) {
    let value:number = e.detail.value<1?1:e.detail.value //最小只能为1
    wx.showLoading({title:'设置中..',mask:true})
    this.setNobodyKeeptime_radarDistance_Command(this.data.keepTime,value)
    this.setData({
      silderValue:value,
    })
  },
  
  /** 滑动-距离设置滑块 */
  distanceSliderChanging: function(e:any){ //滑块的取值
    console.log('distanceSliderChanging => ', e)
    let value:number = e.detail.value
    // 将值除以10，因为滑块值是0.7-12米，但slider组件值是7-120
    let distanceValue = value / 10
    this.setData({
      distanceValue: distanceValue
    })
  },
  
  /** 设置-距离值 如果版本为2.0 设置距离 */
  async distanceBindChange(e:any) {
    let value:number = e.detail.value
    console.log('value => ', value)
    // 将值除以10，因为滑块值是0.7-12米，但slider组件值是7-120
    wx.showLoading({title:'设置中..',mask:true})
    let resultList = []
    let distance = decimalToHexadecimal(value)
    console.log('distance => ', distance)
    // let shift = decimalToHexadecimal(_shift)
    let data = "FDFCFBFA080007000100" + distance + "04030201".replace(/\s/g, '');
    console.log('data => ', data)
    // console.log('这里',data);
    resultList[0] = await newVersionSendBefore()
    resultList[1] = await sendData(data)
    resultList[2] = await newVersionSendSave()
    resultList[3] = await sendAfter()
    console.log('打印设置距离们执行结果：',resultList);
    for (let i = 0; i < resultList.length; i++) {
      if (resultList[i] === false) { //第二段是为了防止死循环
        return toast.info("请稍后再试")
      }
    }
    this.openToast(1,'设置成功')
    wx.hideLoading()
    return
    // 这里可以添加设置距离的命令
    // this.setDistanceCommand(distanceValue)
    // this.setData({
    //   distanceValue: distanceValue,
    // })
    //  setTimeout(async() => {
    //   // this.get_lmd() 不用读灵敏度
    //   await this.readCurrentDeviceConfig()
    //  }, 300);
  },
  /** 切换有无人按钮 */
  SetDevice:function(e:any){
    let status = e.target.dataset.bool
    wx.setStorageSync("showToast",false) 
    this.setData({
      isGetPersonSetting:status,
      isStudy:true
    })
    if (status == 1) {
      this.setPerson()
    }else{
      this.setNobody()
    }
  },
/** 设置有人 */ 
async setPerson(str = "FF CC 0A 00 0A BB AA"){
    this.studyMode(str,'有人')
}, 
/** 设置无人 */ 
async setNobody(str = "FF CC 09 00 09 BB AA"){
  this.studyMode(str,'无人')
},

/** 设置距离命令 */
async setDistanceCommand(distance: number) {
  // 这里实现设置距离的命令
  // 仅在版本为2.0时有效
  if (this.data.version !== '2.0') {
    wx.hideLoading()
    return this.openToast(0, '当前版本不支持此功能')
  }
  
  let resultList = []
  // 将距离转换为16进制字符串
  // 这里假设需要将距离值乘以100转为厘米，然后转为16进制
  let distanceInCm = Math.round(distance * 100)
  let distanceHex = decimalToHexadecimal(distanceInCm)
  
  // 构建命令数据，具体格式需要根据实际协议调整
  let data = "FD FC FB FA 15 00 61 00 " + distanceHex + " 04 03 02 01".replace(/\\s/g, '')
  
  try {
    resultList[0] = await sendBefore()
    resultList[1] = await sendData(data)
    resultList[2] = await sendAfter()
    
    console.log('设置距离执行结果：', resultList)
    
    for (let i = 0; i < resultList.length; i++) {
      if (resultList[i] === false) {
        wx.hideLoading()
        return this.openToast(0, "设置失败，请稍后再试")
      }
    }
    
    this.openToast(1, '设置成功')
    wx.hideLoading()
  } catch (error) {
    console.error('设置距离出错：', error)
    wx.hideLoading()
    this.openToast(0, '设置失败，请稍后再试')
  }
},
  /** 进入学习模式指令 */
async studyMode(str:string,name:string){
  let  resultList: boolean[] = []
  resultList[0] =  await sendBefore()
  resultList[1] =  await sendData(str)
  // resultList[2] =  await sendAfter() ///等重启后再发
  console.log('打印学习模式执行结果：',resultList);
  for (let i = 0; i < resultList.length; i++) {
    if (resultList[i] === false) { 
      return toast.info("请稍后再试")
    }
  }
  // 进入学习模式成功.
  wx.showLoading({title: `${name}模式学习中`,mask:true})
  setTimeout(() => {
    wx.hideLoading({})
    this.openToast(1,`${name}学习成功`)
    setTimeout(() => {
      toast.info(`设备即将重启，请耐心等候`)
      setTimeout(() => {
        sBluetoothManager.disconnectDevice()
        this.closeToast()
        setTimeout(() => {
          this.setData({isGetPersonSetting:0})
          setTimeout(() => {
            this.reConnectionFn(1)
          }, 1500);
        }, 1500);
      }, 2000);
    }, 1000);
  }, 5500);
  },
  /** 查看固件版本 */
  async checkVersion(){
    let resultList = []
    let data = "FF CC 0B 00 0B BB AA".replace(/\s/g, '');
    resultList[0] =  await sendBefore()
    resultList[1] =  await sendData(data)
    resultList[2] =  await sendAfter()
    console.log('打印-读取版本-执行结果：',resultList); 
    for (let i = 0; i < resultList.length; i++) {
      if (resultList[i] === false) { //第二段是为了防止死循环
        return toast.info("请稍后再试")
      }
    }
    
},
  /** 读取V2.0版本当前设备配置 */
  async readNewVersionCurrentDeviceConfig(){
    let resultList = []
    let data = "FDFCFBFA0800080001000400300004030201"; 
    console.log('123123')
    resultList[0] =  await newVersionSendBefore()
    resultList[1] =  await sendData(data)
    resultList[2] =  await newVersionSendAfter()
    console.log('打印-读取当前设备配置-执行结果：',resultList); 
    for (let i = 0; i < resultList.length; i++) {
      if (resultList[i] === false) { //第二段是为了防止死循环
        return toast.info("请稍后再试")
      }
    }
  },
  /** 切换灵敏度选择 */
  async lmdSelected(e:any){ 
    let num:number = e.target.dataset.index
    let list = []
    
    wx.showLoading({title:'设置进行中..',mask:true})
    if (e.target.dataset.index == 1) {
        list = this.data.low_lmdList
        await this.backResult_sendlmdData(list) === true?(this.setData({lmdValue:num}),this.openToast(1,'低灵敏度设置成功'))
        :this.openToast(0,'低灵敏度设置失败')
        wx.hideLoading()
    }else if (e.target.dataset.index == 2) {
        list = this.data.middle_lmdList
        await this.backResult_sendlmdData(list) === true?(this.setData({lmdValue:num}),this.openToast(1,'中灵敏度设置成功'))
        :this.openToast(0,'中灵敏度设置失败')
        wx.hideLoading()
    }else{ //3
        list = this.data.height_lmdList
        await this.backResult_sendlmdData(list) === true?(this.setData({lmdValue:num}),this.openToast(1,'高灵敏度设置成功'))
        :this.openToast(0,'高灵敏度设置失败')
        wx.hideLoading()
    }
  },

  // 新版本的灵敏度设置
  async sensitivitySelected (e) {
    console.log('e => ', e.currentTarget.dataset.index)
    const index = e.currentTarget.dataset.index
    const list = this.data.sensitivityList[index].instructionList
    let resultList = [];
    wx.showLoading({
      title: '设置中...',
      mask: true
    });
    resultList[0] = await newVersionSendBefore()
    if (resultList[0] === false) {
      return toast.info("请稍后再试")
    }
    for (const item of list) {
      const result = await sendData(item)
      if (result === false) {
        return toast.info("请稍后再试")
      }
      resultList.push(result)
    }
    resultList.push(await newVersionSendSave())
    resultList.push(await newVersionSendAfter())
    for (let i = 0; i < resultList.length; i++) {
      if (resultList[i] === false) {
        return toast.info("请稍后再试")
      }
    }
    console.warn('index => ', index)
    this.setData({
      sensitivityIndex: index
    })
    this.openToast(1,'设置成功')
    wx.hideLoading()
    return
  },
  /** 处理灵敏度设置失败 */
  async backResult_sendlmdData(list:number[]):Promise<boolean>{
    let res1 =  await sendBefore()
    console.log('开始使能:',res1);
    if (res1) { // 01.先发使能
      let failCount = 0
      return await new Promise(async(resolve)=>{
        let sendCount_lmd = 0 //初始次数
        let maxFailCount = 2 //最大重发次数

        let timer = setInterval(async ()=>{ //如果没大于9都应该继续发
          if (sendCount_lmd>=9) { //02.出口
            let res2 = await sendAfter()
            console.log('结束使能:',res2);     
            clearInterval(timer)
            return resolve(res2)
          }
          let res = await this.send_lmdData(list,sendCount_lmd)
          console.log(sendCount_lmd,'挡-回复结果：',res);
          
          if (res === false && failCount<maxFailCount) { //01.执行重发 但不能超出重发次数
            failCount+=1
            this.reSendFn(this.send_lmdData,[list,sendCount_lmd],sendCount_lmd,failCount)
          }else if (failCount>maxFailCount || res === false) { //02.超出重发次数
            failCount = 0
            sendCount_lmd = 9
            clearInterval(timer)
            return resolve(false)
          }
          else{ //03.正常走下一步，递归调用九次，调用当然在递归函数里
            failCount = 0
            sendCount_lmd++
          }
        },200)
      }) 
    }else{ //发送使能失败
      return false
    }
  },
  /**发送灵敏度指令 递归调用 */
  async send_lmdData(list:number[],sendCount_lmd:number){ //主要解决：遇到错误就打回重发
      if (sendCount_lmd < 9) {
        // let successValue = 'FD FC FB FA 04 00 64 01 00 00 04 03 02 01'
        let _sendCount_lmd = decimalToHexadecimal(sendCount_lmd)
        let sport_lmd = decimalToHexadecimal(list[sendCount_lmd*2])
        let static_lmd = decimalToHexadecimal(list[(sendCount_lmd*2)+1])
        let data = `FD FC FB FA 14 00 64 00 00 00 ${_sendCount_lmd} 01 00 ${sport_lmd} 02 00 ${static_lmd} 04 03 02 01`
        console.log( sendCount_lmd + '挡----发送的值：',_sendCount_lmd,sport_lmd,static_lmd);
        if (this.outTheFail_lmd(await sendData(data)) === false) { //02.业务
          return false
        }
        return  true
        // 走到这里说明这一档灵敏度值成功了：继续下一档灵敏度
      }
      return  false
  },
  /** 处理灵敏度错误 返回false */
  outTheFail_lmd( bool:boolean | undefined){
    if (bool === false ) { //主要关注失败，失败就打回
      return  false;
    }else{
      return true
    }
  },
  /** 重发 --灵敏度 */
  async reSendFn(fn:Function,dataList:any[],sendCount_lmd:number,failCount:number){
    console.log(`------------正在第${sendCount_lmd}距离门---正在重发${failCount}次------------------`);
    let sendCount = this.data.sendCount
    if (sendCount <3 && dataList.length != 0) {
      this.setData({sendCount:this.data.sendCount += 1})
      return await fn(...dataList)
    }if (sendCount <3 && dataList.length == 0) {
      this.setData({sendCount:this.data.sendCount += 1})
      return await fn()
    }else{
      this.setData({sendCount:0})
      return false
    //  return {status:'发送次数上限',message:'发送次数上限'}
    }
  },
  /** 读取当前设备配置 */
  async readCurrentDeviceConfig(){
    let  resultList = <any>[]
    let str = "FDFCFBFA0200610004030201"
    resultList[0] = await sendBefore()
    console.log('开始');
    
    resultList[1] = await sendData(str)
    console.log('结束');
    
    resultList[2] = await sendAfter()
    // console.log('打印读取设备执行结果：',resultList);
      for (let i = 0; i < resultList.length; i++) {
        if (resultList[i] === false ) {
          toast.info('请稍后再试')
          return;
        }
      }
      // 这里是设备读取参数成功  执行
      console.log('当前设备配置',resultList);
  },
  /*** 是否断开连接了，断开则回连 */
  isConnected(){
    var res = sBluetoothManager.isConnected()
    console.log('设备连接状态',res);
    if (res === false) { //重新连接
      toast.info('设备断开连接，正在准备回连')
      // this.reConnectionFn(1) //自动重连设备
    }
  },
  /** 执行设备回连逻辑 */
  async reConnectionFn(count:number){
    if (sBluetoothManager.getConnectedDevice()?.deviceId != undefined 
    || getCurrentPages()[getCurrentPages().length -1].route.match(/pageSetting/) == null) {
       return;
    }
    console.log('执行次数',count);
    wx.showLoading({title:'正在连接'+count+'次',mask:true})
    const res:any =  await reConnection() 
      if (res === true) {
        wx.hideLoading();
        toast.info('设备已重连成功')
        return this.onShow();
      }else if ( res === false && count === 1) {
        return await this.reConnectionFn(2)
      }else if ( res === false && count === 2) {
        return await this.reConnectionFn(3)
      }else {
          disConnectSoBack('重连失败，请手动连接')
          return;
      }
  },
    /** 获取灵敏度 -设置UI */
  get_lmd(){
      let strConfig = this.data.strConfig 
      let low_lmdList = this.data.low_lmdList
      let middle_lmdList = this.data.middle_lmdList
      let height_lmdList = this.data.height_lmdList
      let static_lmd = hexToDec(strConfig.slice(30,32))
      let sport_lmd = hexToDec(strConfig.slice(48,50))
      wx.hideLoading()
      if (static_lmd === low_lmdList[0] && low_lmdList[1] === sport_lmd) {
        console.log('所以选择了低灵敏度',low_lmdList[0],low_lmdList[1]);
        this.setData({lmdValue:1}) //低灵敏度 
      }else if (static_lmd === middle_lmdList[0] && middle_lmdList[1] === sport_lmd) {
        console.log('所以选择了中灵敏度',middle_lmdList[0],middle_lmdList[1] );
        this.setData({lmdValue:2}) //中灵敏度
      }else if (static_lmd === height_lmdList[0] && height_lmdList[1] === sport_lmd) {
        console.log('所以选择了高灵敏度',height_lmdList[0] ,height_lmdList[1] );
        this.setData({lmdValue:3}) //高灵敏度
      }else  console.log('没找到合适的灵敏度：',sport_lmd,static_lmd);
  },
  //点击返回
  returnSetp: function () {
    wx.navigateBack({ delta: 10 })
  },
  /** 监听数据上报*/
  LisentResponseFn(){
    var that = this;
    let timer = 0;
    var LisentResponse:BleDataCallback = {
      async onReceiveData(res: WechatMiniprogram.OnBLECharacteristicValueChangeCallbackResult) {
          var _data =  ab2hex(res.value);
          let data =  _data.toUpperCase()  //将数据-转换-成大写
          console.log('data => ', data)
          //64697374616E63653A3133330D0A
          if (that.data.version === '2.0' && (data.startsWith('6469') || data.startsWith('4F46'))) {
            const res2: any = hexToString(data) //这里会返回对象 -成功或失败 及指令内容
            
            const isOff = res2.trim() == 'OFF';
            const statusText1 = isOff ? '无人' : res2.split(':')[0] === 'distance' ? '有人' : '';
            
            // 更新状态文本1（有人/无人状态）
            that.setData({
              statusText1: statusText1
            });
            
            // 如果是无人状态，重置距离值为0
            if (isOff) {
              that.setData({
                statusText2: 0,
                distanceValues: [] // 清空距离值数组
              });
            } else if (res2.split(':')[0] === 'distance') {
              // 如果是有人状态，收集距离值
              const distance = +res2.split(':')[1];
              const currentValues = that.data.distanceValues;
              
              // 将新的距离值添加到数组中
              currentValues.push(distance);
              
              // 保留最近的10个值，防止数组过长
              if (currentValues.length > 10) {
                currentValues.shift();
              }
              
              that.setData({
                distanceValues: currentValues
              });
            }
          }
          var res1:any = await AnalyzeData.checkData(data) //这里会返回对象 -成功或失败 及指令内容
          //判定设备是否主动断电 --开始
          clearTimeout(timer)
          timer = setTimeout(() => {
              if (that.data.isStudy == false) {
                wx.getScreenBrightness({
                  success:(res:any)=>{
                    if (res.value != 0) {
                      disConnectSoBack()
                    }
                    console.log('当前屏幕亮度：',res.value);
                  }
                  })
                return;
              }
            }, 1000);
          //判定设备是否主动断电 --结束

            if (res1.status == 'ok') {
              that.setData({
                  statusText1:res1.statusText1, //有/无人状态
                  statusText2:res1.statusText2 //探测距离
                })
            }else if(res1.status === "设备参数"){
              console.warn('res1.status === "设备参数"', that.data.sensitivityList.findIndex((item) => item.value === res1.data.sensitivity)); 
                if (res1.message == '读取成功') {
                    console.log('res => ', res1)
                    const index = that.data.sensitivityList.findIndex(
                      item => item.value === res1.data.sensitivityValue
                    );
                    console.warn('index => ', index);
                    that.setData({
                      silderValue:res1.data.shift,
                      keepTime:res1.data.keepTime,
                      strConfig:res1.str, //data里有对应注释
                      sendCount:0,
                      distanceValue: res1.data.distanceValue,
                      sensitivityIndex: index
                  })
                  that.get_lmd()
                }else{
                  toast.info('设备回复错误')
                }
            }else if (res1.status === "固件版本") {
              console.log('固件版本 ------>',res1.data);
              if (res1.data === '2.0') {
                await that.readNewVersionCurrentDeviceConfig()
              } else {
                await that.readCurrentDeviceConfig()
              }
              that.setData({
                  version:res1.data,
              })
              app.globalData.version = res1.data
            }else if ( res1.status === "有人模式" || res1.status === "无人模式" ) {
              that.setData({
                  isStudy:true
              })
            }
            // if (res1.status === "灵敏度"|| res1.status === "雷达距离门"  ) {
            //   //  console.log('设置灵敏度中：',res1);
            // }else{
            //   // console.log(res1);
            // }
          }
        }
      return LisentResponse

  },

  /**
    * 生命周期函数--显示时调用
    * 当小程序启动，或从后台进入前台显示，会触发 onshow，从二级页面回来时也会触发。页面显示的时候触发 从上个页面返回回来也会触发 执行顺序上 onload先触发 onshow后触发
    */
  async onShow() {
    // wx.showLoading({title:'读取数据中..',mask:true})
    if (BleDataHandler.callbacks.length < 2) {
      BleDataHandler.addCallbacks(this.LisentResponseFn())
    }
    let device:any = sBluetoothManager.getConnectedDevice()
    console.log('连接状态：',device);
    if (device === null) { //如果
      // return await this.reConnectionFn(1)
    }
    
    // 初始化距离滑块的值
    if (this.data.version === '2.0' && !this.data.distanceValue) {
      this.setData({
        distanceValue: 7.0 // 默认值为0.7米
      })
    }
    
    // 初始化距离计算定时器
    if (this.data.distanceCalcTimer) {
      clearInterval(this.data.distanceCalcTimer)
    }
    
    // 设置每两秒计算一次平均距离
    const timer = setInterval(() => {
      if (this.data.statusText1 === '有人' && this.data.distanceValues.length > 0) {
        const avgDistance = this.calculateAverageDistance();
        this.setData({
          statusText2: avgDistance || 0
        });
        console.log('计算平均距离:', avgDistance);
      }
    }, 2000);
    
    this.setData({
      distanceCalcTimer: timer,
      distanceValues: [] // 重置距离值数组
    });
    
    const res = await initBLTChange()
    console.log('初始化结果：', res);
    if (res.message === '此设备无服务') {
      wx.showModal({
        title: "系统提示",
        content:'您的设备疑似缺少主服务，是否尝试去升级完整固件？',
        showCancel: true,
        confirmText: "确定",
        success:(res)=>{
          if (res.confirm) {
            wx.navigateTo({
              url:'../pageFirmware/pageFirmware'
            })
          }else{
            wx.navigateBack({
              delta:10
            })
          }
         
        },
      });
    }
    // console.log('结束使能：',await sendAfter());
    // console.log('读取设备参数：',await this.readCurrentDeviceConfig());
    console.log('读取固件版本：',await this.checkVersion());
    wx.hideLoading()
    if (getCurrentPages()[getCurrentPages().length - 1].route.match(/pageSetting/) != null) {
      this.setData({isStudy:false})
      wx.setStorageSync("showToast",true) 
    }
  },

  /**
    * 生命周期函数--监听页面加载
    */
  onLoad() {

    sBluetoothManager = app.globalData.bluetoothManager
  },
  /**
  * 生命周期函数--监听页面卸载
  页面卸载时会调用此方法，如调用redirectTo方法，navigateBack方法。
  代表一个页面生命周期的结束。
  */
 onUnload: function () {
    this.setData({isStudy:true})
    wx.setStorageSync("showToast",false) 
    console.log('跳转了');
    if (BleDataHandler.callbacks[1] != undefined) {
      console.log('移除监听');
      BleDataHandler.removeCallbacks(BleDataHandler.callbacks[1])
    }
  },
  onHide: function () {
    this.setData({isStudy:true})
    wx.setStorageSync("showToast",false) 
    console.log('小程序从前台进入后台时触发');
    if (BleDataHandler.callbacks[1] != undefined) {
      console.log('移除监听');
      BleDataHandler.removeCallbacks(BleDataHandler.callbacks[1])
    }
    // 清除距离计算定时器
    if (this.data.distanceCalcTimer) {
      clearInterval(this.data.distanceCalcTimer)
    }
    // 可以在这里进行清理工作，如暂停音频、清除定时器等  
  },
  
  /**
   * 计算距离平均值
   * 去掉最大值和最小值，其他数取平均值
   */
  calculateAverageDistance: function() {
    const values = this.data.distanceValues;
    if (values.length < 3) {
      // 如果数据点少于3个，直接返回最后一个值或0
      return values.length > 0 ? values[values.length - 1] : 0;
    }
    
    // 复制数组并排序
    const sortedValues = [...values].sort((a, b) => a - b);
    
    // 去掉最大值和最小值
    const trimmedValues = sortedValues.slice(1, sortedValues.length - 1);
    
    // 计算平均值
    const sum = trimmedValues.reduce((acc, val) => acc + val, 0);
    const average = sum / trimmedValues.length;
    
    return Math.round(average); // 不保留小数，直接四舍五入取整
  }
})