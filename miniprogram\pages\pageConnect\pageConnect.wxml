<!--pages/pageConnect/pageConnect.wxml-->
<!-- <view class="view_header">
    <view class="view_0">设备过滤条件</view>
    <view class="view_1" bindtap="onFilterContent" data-index= '900'>
      <text class="text_1">{{nameText}}</text>
      <image class="image_1" src="/images/<EMAIL>" mode="aspectFit" ></image>
    </view>
</view> -->

<!-- <view class="view_middle">设备列表</view> -->
<view class="head">
    <view class="return" bindtap="returnSetp"></view>
    <text class="title">SR20</text>
</view>
<view class="body">
    <view class='not-device' wx:if="{{deviceList.length == 0}}">
        <image class="not-device-img" src="/images/device-no.png" ></image>
        <view class="not-device-text font-blue">没发现设备,下拉刷新</view>
    </view>

    <view class="view_third" wx:if="{{deviceList.length > 0}}">
        <view class="device-line" 
        hover-class='hover-btn' 
        hover-stay-time="1500"  
        wx:for="{{deviceList}}" 
        wx:key="id" 
        catchtap="onSelectedDevice"
        data-item="{{item}}" 
        data-index="{{item.id}}">

            <image class="dev-left-img"  src="/images/shebei.png" > </image>
            <view class="dev-right" >
                <view class="dev-right-text">{{item.localName || item.name}}</view>
                <image  class="dev-right-img" 
                data-name="{{item.name}}" 
                data-local-name="{{item.localName}}" 
                catchtap="onShowUpdateName" 
                src="/images/bianji.png" > </image>
                <!-- <view wx:if="{{item.deviceId !== connectedDevice.deviceId }}" class="font-blue"  src="/images/bianji.png" >未连接</view> -->
            </view> 
        </view>
    </view>
</view>


<block wx:if="{{hiddenmodalput === false}}">
  <modal hidden="{{hiddenmodalput}}" title="重命名" confirm-text="确定" cancel-text="取消" bindcancel="onCancel" bindconfirm="onConfirm">
  <input class="updateInput" type='text'placeholder="请输入内容" value="{{catchText}}" bindinput="onInput" auto-focus/>
  </modal>
</block> 

<!-- <scroll-view class="view_third" scroll-y refresher-enabled="{{true}}" refresherTriggered="{{triggerde}}" bindrefresherrefresh="onScrollviewRefresh">
        <view class="itemView" wx:for="{{deviceList}}" wx:key="id" bindtap="onSelectedDevice"  data-item="{{item}}" data-index="{{item.id}}">
            <view class="itemView_0">{{item.name}}</view>
            <block wx:if="{{item.deviceId ===connectedDevice.deviceId }}">
            <image class="itemView_1" src="/images/<EMAIL>" mode="aspectFit"></image>
            </block>
        </view>
    </scroll-view> -->
<!-- <block wx:if="{{hiddenmodalput === false}}">
  <modal hidden="{{hiddenmodalput}}" title="设备过滤条件" confirm-text="确定" cancel-text="取消" bindcancel="onCancel" bindconfirm="onConfirm">
  <input type='text'placeholder="请输入内容" value="{{nameText}}" bindinput="onInput" auto-focus/>
  </modal>
</block> -->





