/* pages/pageSetting/pageSetting.wxss */
page {
  background-color: #F4F7FB;
  overflow: hidden;
  height: 99.8vh;
  display: flex;
  flex-direction: column;
  // border: 1px solid red;
}

.main-box {
  padding-top: 40rpx;
  flex: 1;
  overflow-y: auto;
}

.container{
  // background-color: red;
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;

  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}
.header{
  margin-top: -30rpx; //为了不影响全局 返回按钮
  width: 100%;
  height: 422rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  place-items: center;
  font-size: 32rpx;
  position: relative;
  // border: 1px solid red;
}

.ball{
  min-width: 360rpx;
  width: 360rpx;
  max-width: 360rpx;
  height: 360rpx;
  max-height: 360rpx;
  display: flex;
  justify-content: center;
  place-items: center;
  position: relative;
  // border: 2px solid red;
}
/* iPhone 5样式 */
@media only screen and (max-device-width: 320px) {
  /* 在此设置针对iPhone 5的样式 */
  .ball{
    width: 40%;
  }
  .hotButton_box{
    height: 8vh;
  }
  .hotButton_box>view{
    height: 100%;
  }
  .main{
    height: 55vh;
  }
}
/* iPhone 6 Plus样式 */
@media only screen and (max-device-height: 736px) {
  /* 在此设置针对iPhone 5的样式 */
  .ball{
    width: 40%;
  }
  .ball{
    width: 40%;
  }
  .hotButton_box{
    height: 8vh;
  }
  .hotButton_box>view{
    height: 100%;
  }
  .main{
    height: 55vh;
  }
}
/* iPhone XR样式 */
@media only screen and (min-device-width: 414px)  and (min-device-height: 800px){
  /* 在此设置针对iPhone XR的样式 */
  .ball{
    width: 50%;
  }
}
.statusTextBox{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  // background-color: red;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 5vh;
  color: #FFFFFF;
  line-height: 80rpx;
  text-align: center;
  font-style: normal;
  // border: 1px solid ;
}
.keepTime{
  width: 100%;
  margin-top: -10rpx;
  padding: 20rpx;
  text-align: center;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 32rpx;
  color: #000000;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.main{
  width: 92%;
  height: auto;
  overflow: hidden;
  margin: 4rpx 30rpx;
  // margin-left: 2%;
  background-color: white;
  box-shadow: 0rpx 4rpx 2rpx 0rpx rgba(0,0,0,0.15);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  // display: flex;
  // flex-direction: column;
  // justify-content: center;
  // place-items: center;
  // border: 1px solid  red;
}

// 每个大行盒
.itemBox{
  width: 90%;
  margin: 0px 30rpx;
  padding-top: 36rpx;
  padding-bottom: 26rpx;
  border-bottom: 1rpx solid #6d767536;
  display: flex;
  font-size: 32rpx;
  line-height: 33rpx;
  color: #242A38;
  overflow: hidden;
  text-indent: 10rpx;
  font-family: 'PingFang SC-Medium';
}
// 右箭头
.arrow-right{
  margin-left: 5rpx;
  margin-top: 2rpx;
  width: 28rpx;
  height: 30rpx;
  // border: 2rpx solid #7A7A7A;
}
// 滑块的厚度
wx-slider .wx-slider-handle-wrapper{
  height:16rpx;
  background-color: #E8E8E8 !important;
}
// 
.label2{
  margin-left: auto;
  font-size: 28rpx;
  color: #7A7A7A;
  letter-spacing: 1px;
  display: flex;
}
.body_view{
  width: 100%;
  // border: 1px solid ;
}
// 灵敏度icon的容器
.lmd_icon_box{
  margin-top: 20rpx;
  // border: 1px solid red;
  height: 90rpx;
  display: flex;
  justify-content: space-around;
  place-items: center;
}
// 灵敏度icon
.lmd_icon{
  width: 86rpx;
  height: 86rpx;
}
// 灵敏度的label词
.lmd_icon_label{
  width: 100%;
  display: flex;
  justify-content: space-around;
  font-size: 24rpx;
  color: #7A7A7A;
  font-family: 'PingFang SC-Medium';
  // border: 1px solid ;
}
// 红外按钮盒子
.hotButton_box{
  margin-top: auto;
  width: 90%;
  margin-left: 5%;
  padding: 20rpx 0;
  display: flex;
  color: #242A38;
  font-size: 32rpx;
  // border: 1px solid ;
}
// 设置 有人
.havePersonSetting{
  width: 330rpx;
  height: 120rpx;
  background-color: dodgerblue;
  border-radius: 20rpx   20rpx   20rpx   20rpx;
  display: flex;
  justify-content: center;
  place-items: center;
  box-shadow: 0rpx 4rpx 2rpx 0rpx rgba(0,0,0,0.15);
}
// 设置无人
.nobodySetting{
  margin-left: 30rpx;
  width: 330rpx;
  height: 120rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx   20rpx   20rpx   20rpx;
  display: flex;
  justify-content: center;
  place-items: center;
  box-shadow: 0rpx 4rpx 2rpx 0rpx rgba(0,0,0,0.15);
}
// 两个红外按钮的icon
.hotButton_box image{
  margin-top: 0rpx;
  margin-left: 42rpx;
  width: 86rpx;
  height: 86rpx;
}
.white{
  color: white;
}
.blue{
  color: dodgerblue;
}
.black{
  color: #242A38;
}
.bg1{
  background-color: dodgerblue;
}
.bg2{
  background-color: #FFFFFF;
}

// 手撸弹窗
.toast{
  position: fixed;
  top: 0%;
  left: 0%;
  width: 100%;
  height: 100%;
  z-index: 999;
}
.mask{
  position: fixed;
  top: 0%;
  left: 0%;
  width: 100%;
  height: 100%;
  background-color: #3038495e;
  z-index: 999;
}
.toastBox{
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 30%;
  background-color: white;
  border-radius: 30rpx;
  box-shadow: 0 0 2px gray;
  z-index: 1000;
}
.toast_close{
  position: absolute;
  top: -90rpx;
  right: -30rpx;
  width: 80rpx;
  height: 80rpx;
  line-height: 65rpx;
  color: rgba(0,0,0,0.5);
  background-color: rgba(180, 180, 180, 1);
  box-shadow: 0 0 2px #ccc;
  font-size: 70rpx;
  border-radius: 50%;
  text-align: center;
  z-index: 1001;
}
.toast_icon{
  width: 100%;
  height: 80%;
  display: flex;
  justify-content: center;
  place-items: center;
}
.toast_icon>image{
  width: 60%;
  height: 80%;
  object-fit: contain;
}
.toast_text{
  text-align: center;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 40rpx;
  color: #0B83FF;
  line-height: 56rpx;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.blue{
  color: #0B83FF;
}
.red{
  color: crimson;
}
/* 第二个 */
.box{
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40%;
  height: 15%;
  background-color: #0000009d;
  border-radius: 30rpx;
  box-shadow: 0 0 2px gray;
  z-index: 1000;
  display: flex;
  justify-content: center;
  place-items: center;
}
.loading{
  position: absolute;
}

.loading>view{
  position: absolute;
  width: 8rpx;
  height: 20rpx;
  border-radius: 10rpx;
  background-color: #d4d1d1;
}
.loading>view:nth-child(1){
  top: 24rpx;
  left: 0rpx;
  animation: loading infinite 1s;
}
.loading>view:nth-child(2){
  top: 18.5rpx;//14.1442rpx;
  left: 18.5rpx;//14.1442rpx;
  transform: rotate(-45deg);
  animation: loading infinite 1s 0.125s;
}
.loading>view:nth-child(3){
  top: 0rpx;
  left: 24rpx;
  transform: rotate(90deg);
  animation: loading infinite 1s 0.25s;
}
.loading>view:nth-child(4){
  top: -18.5rpx;//-14.1442rpx;
  left: 18.5rpx;//14.1442rpx;
  transform: rotate(45deg);
  animation: loading infinite 1s 0.375s;
}
.loading>view:nth-child(5){
  top: -24rpx;
  left: 0rpx;
  transform: rotate(0deg);
  animation: loading infinite 1s 0.5s;
}
.loading>view:nth-child(6){
  top: -18.5rpx;//-14.1442rpx;
  left: -18.5rpx;//-14.1442rpx;
  transform: rotate(-45deg);
  animation: loading infinite 1s 0.625s;
}
.loading>view:nth-child(7){
  top: 0rpx;
  left: -24rpx;
  transform: rotate(90deg);
  animation: loading infinite 1s 0.75s;
}
.loading>view:nth-child(8){
  top: 18.5rpx; //14.1442rpx;
  left:-18.5rpx; //-14.1442rpx;
  transform: rotate(45deg);
  animation: loading infinite 1s 0.875s;
}
@keyframes loading {
  50% {
    opacity: 0.1;
  }
  100% {
    opacity: 1;
  }
}

.sensitivity-list {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;

  .sensitivity-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .item-name {
      margin-top: 20rpx;
      font-size: 28rpx;
    }
  }
}
