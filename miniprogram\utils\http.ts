import { request } from "./request.js";

/** 获取openid */
export const getOpenidAPI = async (data: { Code: string }) => {
  return await request({
    method: "post",
    url: "/GetOpenId",
    data,
    options: {
      needSign: true, // 需要签名
      needLogin: false, // 不需要登录
    },
  });
};
// 保存设备安全信息
export const getSaveDeviceAPI = async (data: {
  WxOpenId?: string;
  Sn: string;
  Password: string;
  Mnemonic?: string;
  Mobile?: number;
}) => {
  console.log("data", data);
  return await request({
    method: "post",
    url: "/SaveDevicesecurity",
    data,
    options: {
      needSign: true, // 需要签名
      needLogin: false, // 不需要登录
    },
  });
};

// 查询设备密码助记词
export const getDeviceMnemonicAPI = async (data: {
  WxOpenId?: string;
  Sn: string;
}) => {
  console.log("data", data);
  return await request({
    method: "post",
    url: "/GetDeviceMnemonic",
    data,
    options: {
      needSign: true, // 需要签名
      needLogin: false, // 不需要登录
    },
  });
};

// 查询设备密码
export const getDevicePasswordAPI = async (data: {
  WxOpenId?: string;
  Sn: string;
}) => {
  return await request({
    method: "post",
    url: "/GetDevicePassword",
    data,
    options: {
      needSign: true, // 需要签名
      needLogin: false, // 不需要登录
    },
  });
};
// 查询微信号名下所有设备
export const GetAllDevicesAPI= async (data: { WxOpenId?: string; }) => {
  return await request({
    method: "post",
    url: "/GetDevicesByOpenId",
    data,
    options: {
      needSign: true, // 需要签名
      needLogin: false, // 不需要登录
    },
  });
};
// 删除微信号名下的设备信息
export const GetDelDevicesAPI = async (data: { WxOpenId?: string; Sn: string;}) => {
  return await request({
    method: "post",
    url: "/DeleteDeviceByOpenId",
    data,
    options: {
      needSign: true, // 需要签名
      needLogin: false, // 不需要登录
    },
  });
};
// 删除指定微信0penId名下的所有设备信息
export const GetAllDelDevicesAPI = async (data: { WxOpenId?: string }) => {
  return await request({
    method: "post",
    url: "/DeleteDevicesByOpenId",
    data,
    options: {
      needSign: true, // 需要签名
      needLogin: false, // 不需要登录
    },
  });
};
