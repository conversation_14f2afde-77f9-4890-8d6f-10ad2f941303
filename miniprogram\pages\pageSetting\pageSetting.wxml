
<view class="head">
    <view class="return" catchtap="returnSetp"> <image class="return-img" src="/images/fanhui.png" ></image></view>
    <text class="title">参数设置</text>
</view>

<view class="main-box" >
  <view class="header">
      <image src="/images/SR20-wu-icon/zhuangtaixianshi-1.png" class="ball"  bind:tap="disConnect">
        <view class="statusTextBox"> 
          <view style="{{ statusText1 == '有人'?'margin-top:20rpx;':'margin-top:-10rpx;' }}">{{ statusText1  }}</view>
          <view style="font-size: 40rpx;margin-top: -5rpx;" wx:if="{{ statusText1 == '有人' }}">{{ statusText2 + 'cm' }}</view>
        </view>
      </image>
      <view class="keepTime" bind:tap="connect">状态持续时间：{{keepTime}}S</view>
</view>

<!-- 设置有/无人按钮  -->
<view class="hotButton_box">
   <view class="{{isGetPersonSetting == 1?'havePersonSetting bg1':'havePersonSetting bg2'}}" bind:tap="SetDevice" data-bool="1">
      <view class="{{isGetPersonSetting == 1?'white':'black'}}" data-bool="1">设置有人</view>
      <image src="{{isGetPersonSetting == 1?'/images/SR20-wu-icon/youren_gaoliang-1.png':'/images/SR20-wu-icon/youren_moren-1.png'}}"
      data-bool="1"/>
   </view>
   <view class="{{isGetPersonSetting == 2?'nobodySetting bg1':'nobodySetting bg2'}}"  bind:tap="SetDevice" data-bool="2">
      <view class="{{isGetPersonSetting == 2?'white':'black'}}" data-bool="2">设置无人</view>
      <image src="{{isGetPersonSetting == 2?'/images/SR20-wu-icon/wuren_gaoliang-1.png':'/images/SR20-wu-icon/wuren_moren-1.png'}}"
      data-bool="2"/>
   </view>
   <!-- <button bind:tap="sendData2">发送业务指令</button> -->
</view>

<view class="main">
  <!--  -->
  <view class="itemBox" bind:tap="toVersionPage">
    <label>固件版本</label>
    <view style="margin-left: auto;font-size: 28rpx;color: #7A7A7A; letter-spacing: 1px;">V{{version}} </view>
    <image class="arrow-right" src="/images/icon_return.png" style="height: 25rpx;width: 25rpx;margin-top: 3rpx;"></image>
  </view>
  
  <!--  -->
  <view class="itemBox" bind:tap="setNobodyKeepTimer">
    <label>无人持续时间</label>
    <view class="label2">
      <view>{{keepTime}}S</view>
      <image class="arrow-right" src="/images/icon_return.png"></image>
    </view>
  </view>
  <!--  -->
  <view  wx:if="{{version !== '2.0'}}">
    <view class="itemBox" style="border: none;">
      <label>雷达距离门<text style="color: #7A7A7A;font-size: 28rpx;margin-left: 1rpx;">(档位*0.75m)</text> </label>
    <!-- 内容 -->
      <view class="label2">
        <view style="margin-right: 8rpx;">{{silderValue}}档</view>
      </view>
    </view>
    <!-- 滑块 -->
    <view class="itemBox" style="flex-direction: column;padding-top: 0rpx;margin-top: -20rpx;">
        <view>
          <view class="body_view">
              <slider bindchanging="sliderchanging" bindchange="bindchange" min="0" max="8" style="width: 91%;" activeColor="#0B83FF" value="{{silderValue}}"/>
          </view>
          <view style="display: flex; width: 95.8%;margin-left: 2%;margin-top: -10rpx;">
            <view style="color: #7A7A7A;">1</view>
            <view style="margin-left: auto;color: #7A7A7A;">8</view>
          </view>
        </view>
        <view></view>
    </view>
  </view>
  <view wx:else>
    <!-- 新增的距离滑块，仅在version为2.0时显示 -->
    <view class="itemBox" style="border: none;">
      <label>距离设置<text style="color: #7A7A7A;font-size: 28rpx;margin-left: 1rpx;">({{distanceValue}}米)</text> </label>
      <!-- 内容 -->
      <view class="label2">
        <view style="margin-right: 8rpx;">{{distanceValue}}米</view>
      </view>
    </view>
    <!-- 距离滑块 -->
    <view class="itemBox" style="flex-direction: column;padding-top: 0rpx;margin-top: -20rpx;">
        <view>
          <view class="body_view">
              <slider bindchanging="distanceSliderChanging" bindchange="distanceBindChange" min="7" max="120" step="1" style="width: 91%;" activeColor="#0B83FF" value="{{distanceValue*10}}"/>
          </view>
          <view style="display: flex; width: 95.8%;margin-left: 2%;margin-top: -10rpx;">
            <view style="color: #7A7A7A;">0.7米</view>
            <view style="margin-left: auto;color: #7A7A7A;">12米</view>
          </view>
        </view>
        <view></view>
    </view>
  </view>

  
  <!--  -->
  <view class="itemBox" style="border: none;flex-direction: column;">
    <view>灵敏度设置</view>
    <view class="sensitivity-list" wx:if="{{version === '2.0'}}">
      <!--  -->
      <view class="sensitivity-item" wx:for="{{sensitivityList}}" wx:key="index" bind:tap="sensitivitySelected" data-index="{{index}}">
        <image class="" style="width: 80rpx; height: 80rpx;" src="{{index === sensitivityIndex ? item.selectIcon : item.icon}}" mode="widthFix"></image>
        <view class="item-name">
          {{ item.name }}
        </view>
      </view>
    </view>
    <view class="" wx:else>
      <view class="lmd_icon_box">
        <image src="{{lmdValue == 1?'/images/SR20-wu-icon/dilingmin_gaoliang-1.png':'/images/SR20-wu-icon/dilingmin_moren-1.png'}}" class="lmd_icon" bind:tap="lmdSelected" data-index="1"/>
        <image src="{{lmdValue == 2?'/images/SR20-wu-icon/zhonglingmin_gaoling-1.png':'/images/SR20-wu-icon/zhonglingmin_moren-1.png'}}" class="lmd_icon" bind:tap="lmdSelected" data-index="2"/>
        <image src="{{lmdValue == 3?'/images/SR20-wu-icon/gaolingmin_gaoling-1.png':'/images/SR20-wu-icon/gaolingmin_moren-1.png'}}" class="lmd_icon" bind:tap="lmdSelected" data-index="3"/>
      </view>
      <view class="lmd_icon_label">
        <view class="{{lmdValue == 1?'blue':'black'}}" bind:tap="lmdSelected" data-index="1">低灵敏度</view>
        <view class="{{lmdValue == 2?'blue':'black'}}" bind:tap="lmdSelected" data-index="2">中灵敏度</view>
        <view class="{{lmdValue == 3?'blue':'black'}}" bind:tap="lmdSelected" data-index="3">高灵敏度</view>
      </view>
    </view>
    
    
  </view>

  <!-- 加载 -->
    <!-- loading -->
    <!-- <view class="box">
      <view class="loading">
        <view></view><view></view><view></view><view></view>
        <view></view><view></view><view></view><view></view>
      </view>
      <view class="loadingText"> 加载中..</view>
    </view>  -->

  <!-- 手撸弹窗 -->
</view>
</view>
  <view class="toast" wx:if="{{isShowToast}}"  bind:tap="closeToast">
    <view class="mask"></view>
    <view class="toastBox">
      <view class="toast_close"> × </view>
      <view class="toast_icon">
        <image src="{{toastSuccess === true?'../../images/chenggong.png':'../../images/shibai.png'}}" mode="aspectFit"/>
      </view>
      <view class="toast_text {{toastSuccess === true?'blue':'red'}}">
        {{toastText}}
      </view>
    </view>
  </view>