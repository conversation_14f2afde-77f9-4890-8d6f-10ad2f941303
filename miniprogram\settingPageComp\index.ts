import { IAppOption } from "../../typings/index"
const app = getApp<IAppOption>()
const { store } = app.globalData
import {BluetoothManager } from "../lib/bluetoothManager"
import { BleSendDataHandler } from '../lib/ble-data-handler'
import { backConnectPage } from "../lib/log"
var sBluetoothManager: BluetoothManager = app.globalData.bluetoothManager


// 数据
let sendCount = 0;//重发次数
let maxCount = 2; //最大次数
let isShow = false; //是否有人调用了 返回弹窗

/**
* 将字符串转换成ArrayBufer
*/
function string2buffer(str: string) {
  let val = ""
  if (!str) return;
  let length = str.length;
  let index = 0;
  let array = []
  while (index < length) {
    array.push(str.substring(index, index + 2));
    index = index + 2;
  }
  val = array.join(",");
  /** 将16进制转化为ArrayBuffer */
  return new Uint8Array(val.match(/[\da-f]{2}/gi).map(function (h) {
    return parseInt(h, 16)
  })).buffer
}
/**
 * 将ArrayBuffer转换成字符串
 */
function ab2hex(buffer: Uint8Array) {
  var hexArr = Array.prototype.map.call(
    new Uint8Array(buffer),
    function (bit) {
      return ('00' + bit.toString(16)).slice(-2)
    })
  return hexArr.join('');
}
/** 是否开启蓝牙 */
async function isOpenTheBluetooth() {
  return await new Promise((resolve) => {
    // 检测是否开启了蓝牙
    wx.getBluetoothAdapterState({
      success: function (res) {
        if (res.available) {
          resolve({ status: true })
        } else {
          resolve({ status: false, message: '请打开蓝牙！' })
        }
      },
      fail: function (res) {
        resolve({ status: false, message: '请打开蓝牙！' })
      }
    })
  })
}
/** 是否与设备断开连接 */
async function isConnectDevice() {
  return await new Promise((resolve) => {
    // 检测连接状态
    wx.getConnectedBluetoothDevices({
      success: function (res) {
        if (res.devices.length > 0) {
          resolve({ status: true, message: '设备已连接' })
          return;
        } else {
          resolve({ status: false, message: '设备已断开连接' })
          return;
        }
      },
      fail: function (res) {
        resolve({ status: false, message: '设备已断开连接' })
        return;
      }
    })
  })
}
/** 根据当前设备情况作出提示 */
async function checkBluetoothConnectStatus() {
  let res = await isOpenTheBluetooth()
  if (res.status) {
    return await isConnectDevice()
  }
  return await res;
}
/** 失去连接，则返回连接页 */
function disConnectSoBack(content = '设备连接已断开，请重新连接'){ 
  const isShow = wx.getStorageSync('isShow')
  if (isShow == 'true' && getCurrentPages()[getCurrentPages().length - 1].route.match(/pageConnect/) != null) {
    return;
  }
  wx.setStorageSync('isShow',true)//这期间就不能有人再调用了
  wx.setStorageSync("showToast",false)
  wx.showModal({
    title: "温馨提示",
    content,
    showCancel: false,
    confirmText: "确定",
    success:()=>{
      wx.setStorageSync('isShow',false)//这期间就不能有人再调用了
        wx.navigateBack({
          delta:backConnectPage()
        })
      }
  });
}
/** 回连设备 */
async function reConnection():Promise<boolean> {
  sBluetoothManager.connectDevice(app.globalData.device,false)
  return await new Promise((resolve) => {
    setTimeout(() => {
      var device = sBluetoothManager.getConnectedDevice()
      // console.log('打印一下连接后',device);
      if (device?.deviceId != undefined) {
        resolve(true)
      }else{
        resolve(false)
      }
    }, 2500);
  })
  // 这里调用可以自己重连三次
  // return await new Promise(async (resolve) => {
  //   var count = 0
  //   var timer =  setInterval(() => {
  //     if (count <3) {  //连接成功
        // var device = sBluetoothManager.getConnectedDevice()
        // if (device?.deviceId != undefined) {
        //   clearInterval(timer)
        //   resolve(true)
        // }else{
        //   count++
        // }
  //     }else{ //多次连接失败
  //       clearInterval(timer)
  //       resolve(false)
  //     }
  //   }, 1500)
  // })
}
/** 01.获取服务 */
const getService: any = async () => {
  return await new Promise((resolve) => {
    wx.getBLEDeviceServices({
      deviceId: app.globalData.device.deviceId,
      success: async function (res) {
        for (let i = 0; i < res.services.length; i++) {
          if (res.services[0].uuid === '0000FFF0-0000-1000-8000-00805F9B34FB') {
            store.serviceId = res.services[0].uuid //服务ID
          }
        }
        // console.log('所有服务：',res);
        if (store.serviceId.length === 0) {
          resolve(({ status: 'bad', message: '此设备无服务' }))
        } else {
          resolve(await getCharacter(store.serviceId))
        }
      }, fail: (err) => {
        resolve({ status: 'bad', message: '此设备无服务' })
      }
    })
  });
}
/** 02.获取设备特征:读or写or响应 */
const getCharacter = async (dev_service_uuid: string) => {
  return await new Promise((resolve) => {
    wx.getBLEDeviceCharacteristics({
      deviceId: app.globalData.device.deviceId,
      serviceId: dev_service_uuid,
      success: async function (res) {
        // console.log('这个是所有特征值：',res);
        for (var i = 0; i < res.characteristics.length; i++) {//2个值
          var model = res.characteristics[i] //听说ios端uuid要大写才能被检测到!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
          var uuid = model.uuid
          switch (true) {
            case model.properties.notify: //01.如果为上报数据/只响应
              store.notifyId = uuid
              // console.log('看看响应特征：',model)
              break;
            case model.properties.write: //02.如果支持写入 
              store.writeId = uuid
              // console.log('看看写入特征：',model)
              break;
            default:
              return resolve({ status: 'bad', message: '此设备无服务' })
          }
        }
        // 循环后
        if (store.writeId != '' || store.notifyId != '' || store.writeId != undefined) { //写入
          return resolve(lisenCharacterDataChange(store.notifyId))
        } else { //响应
          return resolve({ status: 'bad', message: '此设备无服务' })
        }
      },
      fail: (err) => {
        return resolve({ status: 'bad', message: '此设备无服务' })
      }
    })
  });
}
/** 03.监听特征值变化 */
const lisenCharacterDataChange = async (uuid: string) => {
  return await new Promise((resolve) => {
    wx.notifyBLECharacteristicValueChange({ //启用监听蓝牙特征值的变化
      state: true, // 启用 notify 功能
      deviceId: app.globalData.device.deviceId,
      serviceId: store.serviceId,
      characteristicId: uuid,  //第一步 开启监听 notityid  第二步发送指令 write
      success: function () {
        return resolve({ status: 'ok', message: '开始监听' })
      }, fail: (err) => {
        return resolve({ status: 'bad', message: '请断开设备重新连接' })
      }
    })
  });

}
/** 04.发送指令 --自带三次重发 */
const sendData = async (_str: string): Promise<any> => { //1.发送的指令  2.要接收到的指令
  var str = _str.replace(/\s/g, '');
  console.log('发送指令',str);
  var buffer: any = string2buffer(str) //开始通知 
  return await new Promise((resolve) => {
    setTimeout(() => {
      BleSendDataHandler._sendData({
        deviceId: app.globalData.device.deviceId,
        serviceId: app.globalData.store.serviceId,
        characteristicId: app.globalData.store.writeId,
        data: {
          buffer
        }}, {complete(res:boolean){
          if (res) {
              console.log('发送成功 ==> ', res)
              if (sBluetoothManager.getConnectedDevice() != null || sBluetoothManager.getConnectedDevice() != undefined) {
                return resolve(true)
              }
              return resolve(false)
          }
          return resolve(false)
        }})
    }, 100);
  });
 
}
/** 05.发送使能指令 */
const sendBefore = async ():Promise<boolean> => {
  var str = 'FDFCFBFA0400FF00010004030201' //开始通知 
  return await sendData(str)
}
/**  06.结束使能配置指令 */
const sendAfter = async ():Promise<boolean> => {
  var str = 'FDFCFBFA0200FE0004030201' //结束使能指令
  return await sendData(str)
}

/** 01. 新版本发送使能指令 */
const newVersionSendBefore = async ():Promise<boolean> => {
  var str = 'FDFCFBFA0400FF00020004030201' //开始通知 
  return await sendData(str)
}

/** 03. 新版本发送保存使能指令*/
const newVersionSendSave = async ():Promise<boolean> => {
  var str = 'FDFCFBFA0200FD0004030201'
  return await sendData(str)
}

/** 04. 新版本结束使能配置指令*/
const newVersionSendAfter = async ():Promise<boolean> => {
  var str = 'FDFCFBFA0200FE0004030201' //结束使能指令
  return await sendData(str)
}

/** 00.初始化监听特征值 */
const initBLTChange: any = async () => {
  return await getService()
}

/**  发送次数上限 */
async function overSendCount(str: string):Promise<boolean> {
  // console.log('执行重发',...data);
  if (str != undefined) {//重新调用
    return await sendData(str)
  } 
  return false
}
/** 发送指令，且设重发次数 --最少会执行6次 */
const reSendData = async ( str: string,_maxCount = 1):Promise<boolean> => {
  _maxCount !=1?maxCount = _maxCount:maxCount = maxCount
  console.log('重试:', sendCount);
  if (sendCount > maxCount) {//首先判断是否超过最大次数
    sendCount = 0
    return false
  }
  const res = await overSendCount(str)
  // console.log(sendCount,'每次重执行',res);
  if (res) {
    sendCount = 0
    return res
  } else {
    sendCount++
    return await reSendData(str)
  }

}

/** 16进制转十进制且小端*/
function hexToDec(hexString: string) {
  // 删除前导0x或0X
  hexString = hexString.replace(/^0[xX]/, '');

  // 将十六进制数字转换为字节数组
  const bytes = [];
  for (let i = 0; i < hexString.length; i += 2) {
    bytes.push(parseInt(hexString.substr(i, 2), 16));
  }
  // 将字节数组转换为小端字节顺序的十进制数字
  let dec = 0;
  for (let i = 0; i < bytes.length; i++) {
    dec += bytes[i] << (i * 8);
  }
  return dec;
}
/* 十进制转换16进制,并且转小端模式数据*/
function decimalToHexadecimal(decimal:number) {
  return (decimal + 2 ** 32).toString(16).match(/\B../g).reverse().join ``;
}

function hexToString(hex: string) {
    let str = '';
    for (let i = 0; i < hex.length; i += 2) {
      str += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
    }
    return str;
  }
export {
  ab2hex,
  string2buffer,
  initBLTChange, //建立写入链接
  sendData,//发送指令
  sendBefore, //--发送使能指令
  sendAfter,//结束使能指令
  checkBluetoothConnectStatus,//检测当前蓝牙连接状态
  overSendCount, // 可重发指令
  hexToDec,// 16进制转10且小端
  reConnection, //重连
  disConnectSoBack,// 断开连接则返回首页
  decimalToHexadecimal,
  newVersionSendBefore,//新版本发送使能指令
  newVersionSendSave,//新版本发送保存使能指令
  newVersionSendAfter,//新版本结束使能指令
  hexToString, // 16进制转字符串
}