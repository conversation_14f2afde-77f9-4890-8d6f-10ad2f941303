// pages/pageDeviceList/pageDeviceList.ts

import {
  GetAllDevicesAPI,
  GetDelDevicesAPI,
  GetAllDelDevicesAPI,
} from "../../utils/http";
import toast from "../../utils/toast.js";
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 已登录的设备列表
    loginDeviceList: [],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 初始化页面数据
    this.GetAllDevices();
  },

  /**
   * 返回上一页
   */
  returnSetp() {
    wx.navigateBack({
      delta: 1,
    });
  },
  // 所有设备
  async GetAllDevices() {
    try {
      let WxOpenId = wx.getStorageSync("openid");
      let obj = {
        WxOpenId,
      };
      const res = await GetAllDevicesAPI(obj);
      console.log("所有设备", res);
      if (res.Code == 1) {
        this.setData({
          loginDeviceList: res.Data || [],
        });
      } else {
        this.setData({
          loginDeviceList: [],
        });
      }
    } catch (err) {
      console.log("失败", err);
    }
  },
  // 删除单个设备
  async onCancelDevice(e: any) {
    try {
      let WxOpenId = wx.getStorageSync("openid");
      let Sn = e.currentTarget.dataset.sn;
      let obj = {
        WxOpenId,
        Sn,
      };
      const res = await GetDelDevicesAPI(obj);
      console.log("单个设备", res);
      if (res.Code == 1) {
        toast.success(res.Message);
        setTimeout(() => { 
          this.GetAllDevices();
        }, 1000);
      } else {
        toast.fail(res.Message || "单个设备失败");
      }
    } catch (err) {
      console.log("单个设备失败", err);
    }
  },
  // 删除全部设备
  async onAll() {
    try {
      let WxOpenId = wx.getStorageSync("openid");
      let obj = {
        WxOpenId,
      };
      const res = await GetAllDelDevicesAPI(obj);
      console.log("全部设备", res);
      if (res.Code == 1) {
        toast.success(res.Message);
        setTimeout(() => {
          this.GetAllDevices();
        }, 1000);
      } else {
        toast.fail(res.Message || "全部设备失败");
      }
    } catch (err) {
      console.log("全部设备失败", err);
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
});
