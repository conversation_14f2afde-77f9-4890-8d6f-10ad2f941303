/** 解析设备回复数据 */
let cacheResponseDataList:string[] = []//暂存返回的不合法数据，等两条一起合并
let lisenData = 'F4F3F2F1 F8F7F6F5'.replace(/\s/g, '') //数据上报
let readVersion = 'FFBB0B00 XXXX 00BBAA'.replace(/\s/g, '') //读取固件版本  XXXX是版本号 FFBB0B00 0102 00BBAA
let setNobodyKeeptime_radarDistance_Command = 'FD FC FB FA 04 00 60 01 00 00 04 03 02 01'.replace(/\s/g, '') //无人持续时间和雷达距离门
let sendBefore = 'FD FC FB FA 08 00 FF 01 00 00 01 00 40 00 04 03 02 01'.replace(/\s/g, '') //开始使能
let sendAfter = 'FD FC FB FA 04 00 FE 01 00 00 04 03 02 01'.replace(/\s/g, '') //结束使能
let lmd = 'FD FC FB FA 04 00 64 01 00 00 04 03 02 01'.replace(/\s/g, '') //灵敏度
let nobody = 'FF BB 01 01 02 BB AA'.replace(/\s/g, '')  //无人
let somebody = 'FF BB 01 01 02 BB AA'.replace(/\s/g, '')  //有人
export default {
  /** 0.先检查回复的数据是否合法 */
 async checkData(str:string):Promise<object> {   //str的长度最少要大于16，不大于就看是否存在特殊情况
    return await new Promise( async (resolve) => {
      let head = str.slice(0,8)
      if (str.length === 46 && head === lisenData.slice(0,8)) { //01.这是上报数据格式
        return resolve(await this.receiveDataCallback(str))
      }else{
        return resolve(this.receiveCommandCallback(str))
      }
    })
  },

 /** 1.处理发送 */  //-----str:设备返回;
  async receiveCommandCallback(str:string):Promise<object>{
    let head = str.slice(0,8)
    let end = str.slice((str.length-6),str.length)
    return await new Promise( async (resolve) => {//读取固件版本  XXXX是版本号 FFBB0B00 0102 00BBAA

      if(str.slice(0, 20) === 'FDFCFBFA100008010000' && str.slice((str.length-8),str.length) === '04030201') {
        console.log()
        const data = {
          distanceValue:this.hexToDec(str.slice(20,28)) / 10 , //最大距离门 
          keepTime:this.hexToDec(str.slice(28,36)), //无人持续时间, 16)) //无人持续时间
          sensitivityValue:str.slice(36,44) //灵敏度
        }
        console.warn('str => ', str)
        console.warn('str.slice(20,28) => ', str.slice(20,28))
        console.warn('str.slice(28,36) => ', str.slice(28,36))
        console.warn('str.slice(36,44) => ', str.slice(36,44))
        console.warn('data => ', data)
        return resolve({status:'设备参数',message:'读取成功',data,str})
      } else if (head == readVersion.slice(0,8) && end == readVersion.slice((readVersion.length-6),readVersion.length)) { //02读取固件版本成功  --特事特办 长度18
        let version1 = this.hexToDec(str.slice(8,10))
        let version2 = this.hexToDec(str.slice(10,12))
        return resolve({status:'固件版本',data:version1 + '.' + version2,str})
      }else if ( str.slice(22,24) == '08' && str.length === 76) { //03.读取设备参数成功  --特事特办 08是铁定的，因为最大只能8档
        return resolve(await this.AnalyzeDeviceConfigData(str))
      }else if ( str == setNobodyKeeptime_radarDistance_Command && str.length === 28) { //04.设置无人持续时间和雷达距离门  --特事特办
        return resolve({
          status:'雷达距离门',
          message:'设置成功'
        })
      }else if (str === sendBefore) {
          return resolve({
            status:'使能指令',
            message:'设置成功'
          })
      } else if (str === sendAfter) {
          return resolve({
            status:'结束使能指令',
            message:'设置成功'
          })
      }else if (str == lmd) {
        return resolve({
          status:'灵敏度',
          message:'设置成功',
          str
        })
      }else if (str == nobody) {
        return resolve({
          status:'无/有人模式',
          message:'设置成功',
          str
        })
      }else if (str == somebody) {
        return resolve({
          status:'无/有人模式',
          message:'设置成功',
          str
        })
      } else if (str.startsWith("FDFCFBFA080008010000")) {
        console.log('获取到时间')
        return
      } else{
          return resolve({
            status:'bad',
            message:'数据不合法',
            str
          })
      }
    })
  },
 /** 2.数据上报回复处理 */
  async receiveDataCallback(str:string):Promise<object>{    
    if (cacheResponseDataList.length === 2) { //01.如果有暂存数据,就拼起来;如果没有则代表一次返回的数据是合法的长度
      // 还得保证头和尾不能拼错
      cacheResponseDataList[0].length>cacheResponseDataList[1].length
      ?str = cacheResponseDataList[0] + cacheResponseDataList[1]
      :str = cacheResponseDataList[1] + cacheResponseDataList[0];
      cacheResponseDataList = [];
    }
    // 02.安全手段
    if (str.length <20 && str.length > 70) {
      str.length > 80? cacheResponseDataList = []:''
      return {status:'bad',message:'数据粘包'};
    }
    // 03.数据合法,执行数据解析
    if (str.length == 46) { //上报数据  判断是否合法 固定长度为46
      this.hexToDec(str.slice(8,12)) //转10进制
      cacheResponseDataList = [] //清空
      return await this.analyzeDataLog(str.slice(16,34))
    }
    // 04.如果都不满足则暂存起来,重新,走拼接
    str.length > 70? cacheResponseDataList = []:cacheResponseDataList.push(str)
    return {status:'bad',message:'待拼包'}; //上报数据会一直发，不需要重载函数，故return就行
  },
  /** 3.解析页面需要的上报数据 */ 
  async analyzeDataLog (str:string):Promise<object> {
    let status = this.hexToDec(str.slice(0,2)) != '00'?'有人':'无人'
    if (status == '有人') {
        let sport_targetDistance:any = Number(this.hexToDec(str.slice(2,6)))
        let static_targetDistance:any  = Number(this.hexToDec(str.slice(8,12)))
        return {
          status:'ok',
          statusText1:status,
          statusText2:static_targetDistance >sport_targetDistance?sport_targetDistance:static_targetDistance
        }
    }else{
        return {
          status:'ok',
          statusText1:status,
          statusText2:''
        }
    }
  },
  /** 解析当前设备参数配置 */
  async AnalyzeDeviceConfigData(str:string):Promise<object> {
      let data ={
        isSuccess:str.slice(16,20) == '0000'?true:false,//查询是否成功
        shift:this.hexToDec(str.slice(24,26)), //最大距离门 --档位
        keepTime:this.hexToDec(str.slice(64,68)) //无人持续时间
      }
      if (data.isSuccess == true) {
        cacheResponseDataList = []
        return {status:'设备参数',message:'读取成功',data,str}
      }else{
        cacheResponseDataList = []
        return {status:'设备参数',message:'设备配置读取失败',str}
      }
  },
    /** 16进制转十进制且小端*/
  hexToDec(hexString:string) {
       // 删除前导0x或0X
       hexString = hexString.replace(/^0[xX]/, '');
        
        // 将十六进制数字转换为字节数组
        const bytes = [];
        for (let i = 0; i < hexString.length; i += 2) {
          bytes.push(parseInt(hexString.substr(i, 2), 16));
        }
        
        // 将字节数组转换为小端字节顺序的十进制数字
        let dec = 0;
        for (let i = 0; i < bytes.length; i++) {
          dec += bytes[i] << (i * 8);
        }
        
        return dec;
  },

}