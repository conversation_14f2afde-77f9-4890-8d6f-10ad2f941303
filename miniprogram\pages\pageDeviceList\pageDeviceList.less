

/* 页面整体布局 */
page {
  background-color: #F4F7FB;
  height: 100vh;
}
.head {
  background-color: white;
  padding-top: 100rpx;
  padding-bottom: 20rpx;
  height: fit-content;
  // box-shadow: 0px 8px 4px rgb(245, 245, 245);
  border-bottom: 2px solid rgb(204, 203, 203);
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 9999;
}
.refresh-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-img {
  width: 40rpx;
  height: 40rpx;
}

.main{
  margin-top: 200rpx;
  height: calc(100vh - 200rpx);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.empty-img {
  width: 300rpx;
  height: 280rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  font-family: 'PingFang SC-Medium';
}

/* 设备列表 - 卡片式布局 */
.device-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin: 0 30rpx;
}

.device-item {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.t1{
  width: 150rpx;
  display: inline-block;
  margin: 4rpx 0;
  color: #666;
}



/* 取消按钮 */
.cancel-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a5a);
  color: #fff;
  border: none;
  border-radius: 30rpx;
  padding: 16rpx 30rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
  margin-top: 20rpx;
  width: 70vw !important;
}

.orange-button {
  background-color: #FF9900;
  color: #fff;
  width: 80vw !important;
}


